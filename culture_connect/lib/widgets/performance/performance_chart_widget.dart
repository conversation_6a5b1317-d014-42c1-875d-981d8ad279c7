// Flutter imports
import 'package:flutter/material.dart';

// Third-party imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/models/performance/performance_metric.dart';
import 'package:culture_connect/widgets/progress/animated_progress_bar.dart';

/// Enum for different chart types
enum PerformanceChartType {
  /// Line chart for trends over time
  line,

  /// Bar chart for comparisons
  bar,

  /// Gauge chart for current values
  gauge,

  /// Area chart for cumulative data
  area,
}

/// Widget for displaying performance metrics in various chart formats
class PerformanceChartWidget extends ConsumerStatefulWidget {
  /// The performance metrics to display
  final List<PerformanceMetric> metrics;

  /// The type of chart to display
  final PerformanceChartType chartType;

  /// The title of the chart
  final String title;

  /// The height of the chart
  final double? height;

  /// Whether to show the legend
  final bool showLegend;

  /// Whether to show grid lines
  final bool showGrid;

  /// Whether to animate the chart
  final bool animate;

  /// Callback when a data point is tapped
  final void Function(PerformanceMetric metric)? onMetricTapped;

  /// Creates a new performance chart widget
  const PerformanceChartWidget({
    super.key,
    required this.metrics,
    required this.chartType,
    required this.title,
    this.height,
    this.showLegend = true,
    this.showGrid = true,
    this.animate = true,
    this.onMetricTapped,
  });

  @override
  ConsumerState<PerformanceChartWidget> createState() =>
      _PerformanceChartWidgetState();
}

class _PerformanceChartWidgetState extends ConsumerState<PerformanceChartWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.animate) {
      _animationController.forward();
    } else {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          side: BorderSide(
            color: theme.colorScheme.outline.withAlpha(51),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(theme),
              const SizedBox(height: AppTheme.spacingMedium),
              SizedBox(
                height: widget.height ?? 200,
                child: widget.metrics.isEmpty
                    ? _buildEmptyState(theme)
                    : _buildChart(theme),
              ),
              if (widget.showLegend && widget.metrics.isNotEmpty) ...[
                const SizedBox(height: AppTheme.spacingMedium),
                _buildLegend(theme),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
      children: [
        Icon(
          _getChartIcon(),
          color: theme.colorScheme.primary,
          size: 20,
        ),
        const SizedBox(width: AppThemeacingSmall),
        Expanded(
          child: Text(
            widget.title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight600,
            ),
          ),
        ),
        if (widget.metrics.isNotEmpty)
          Text(
            '${widget.metrics.length} metrics',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 48,
            color: theme.colorScheme.onSurfaceVariantithAlpha(128),
          ),
          const SizedBox(height: AppThemeacingSmall),
          Text(
            'No performance data available',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppThemeacingSmall),
          Text(
            'Performance metrics will appear here once collected',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariantithAlpha(179),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildChart(ThemeData theme) {
    switch (widget.chartType) {
      case PerformanceChartType.line:
        return _buildLineChart(theme);
      case PerformanceChartType.bar:
        return _buildBarChart(theme);
      case PerformanceChartType.gauge:
        return _buildGaugeChart(theme);
      case PerformanceChartType.area:
        return _buildAreaChart(theme);
    }
  }

  Widget _buildLineChart(ThemeData theme) {
    // TODO: Implement actual line chart with fl_chart library
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.show_chart,
            size: 48,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Line chart visualization',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Showing ${widget.metrics.length} data points',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withAlpha(179),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart(ThemeData theme) {
    // Create simplified bar chart visualization
    final maxValue =
        widget.metrics.map((m) => m.value)educe((a, b) => a > b ? a : b);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: widget.metrics.take(8).map((metric) {
        final normalizedHeight = (metric.value / maxValue) * 150;
        final color = _getMetricColor(theme, metric.type);

        return GestureDetector(
          onTap: () => widget.onMetricTapped?.call(metric),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                width: 20,
                height: normalizedHeight,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(4),
                  ),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                metric.value.toStringAsFixed(0),
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 10,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildGaugeChart(ThemeData theme) {
    if (widget.metrics.isEmpty) return const SizedBox.shrink();

    final latestMetric = widget.metrics.last;
    final maxValue = _getMaxValueForMetricType(latestMetric.type);
    final progress = (latestMetric.value / maxValue).clamp(0.0, 1.0);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 120,
            height: 120,
            child: AnimatedProgressBar(
              progress: progress,
              type: ProgressBarType.circular,
              color: _getMetricColor(theme, latestMetric.type),
              showPercentage: false,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            '${latestMetric.value.toStringAsFixed(1)} ${latestMetric.unit}',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: _getMetricColor(theme, latestMetric.type),
            ),
          ),
          Text(
            latestMetric.type.displayName,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAreaChart(ThemeData theme) {
    // TODO: Implement actual area chart with fl_chart library
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.area_chart,
            size: 48,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Area chart visualization',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegend(ThemeData theme) {
    final metricTypes = widget.metrics.map((m) => m.type).toSet().toList();

    return Wrap(
      spacing: AppThemeacingMedium,
      runSpacing: AppThemeacingSmall,
      children: metricTypes.map((type) {
        final color = _getMetricColor(theme, type);
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: AppThemeacingSmall),
            Text(
              type.displayName,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  IconData _getChartIcon() {
    switch (widget.chartType) {
      case PerformanceChartType.line:
        return Icons.show_chart;
      case PerformanceChartType.bar:
        return Icons.bar_chart;
      case PerformanceChartType.gauge:
        return Icons.speed;
      case PerformanceChartType.area:
        return Icons.area_chart;
    }
  }

  Color _getMetricColor(ThemeData theme, MetricType type) {
    switch (type) {
      case MetricType.startup:
        return theme.colorScheme.primary;
      case MetricType.transition:
        return theme.colorScheme.secondary;
      case MetricType.api:
        return theme.colorScheme.tertiary;
      case MetricType.memory:
        return Colors.orange;
      case MetricType.battery:
        return Colors.green;
      case MetricType.rendering:
        return Colors.purple;
    }
  }

  double _getMaxValueForMetricType(MetricType type) {
    switch (type) {
      case MetricType.startup:
        return 5000; // 5 seconds
      case MetricType.transition:
        return 1000; // 1 second
      case MetricType.api:
        return 2000; // 2 seconds
      case MetricType.memory:
        return 500; // 500 MB
      case MetricType.battery:
        return 100; // 100% per hour
      case MetricTypeendering:
        return 16; // 16ms (60fps)
    }
  }
}
