import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/models/translation/pronunciation_model.dart';
import 'package:culture_connect/models/translation/translation_pronunciation.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/voice_translation/audio_playback_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A service for handling pronunciation information
class PronunciationService {
  /// The HTTP client
  final http.Client _client;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The audio playback service
  final AudioPlaybackService _playbackService;

  /// The logging service
  final LoggingService _loggingService;

  /// The connectivity service
  final Connectivity _connectivity;

  /// Whether the service is currently online
  bool _isOnline = true;

  /// Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// Cache for pronunciation information
  final Map<String, TranslationPronunciation> _pronunciationCache = {};

  /// Stream controller for pronunciation events
  final StreamController<String> _pronunciationEventsController =
      StreamController<String>.broadcast();

  /// The error stream controller
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  /// Whether to use pronunciation guidance
  bool _usePronunciationGuidance = true;

  /// Whether to use IPA notation
  bool _useIpaNotation = true;

  /// Whether to use simplified phonetics
  bool _useSimplifiedPhonetics = true;

  /// Whether to use syllable breakdown
  bool _useSyllableBreakdown = true;

  /// Whether to show difficult pronunciations only
  bool _showDifficultOnly = false;

  /// Whether to auto-play pronunciation audio
  bool _autoPlayPronunciation = false;

  /// Creates a new pronunciation service
  PronunciationService(
    this._client,
    this._prefs,
    this._playbackService,
    this._loggingService,
    this._connectivity,
  ) {
    _loadPreferences();
    _initConnectivity();
  }

  /// Stream of pronunciation events
  Stream<String> get pronunciationEvents =>
      _pronunciationEventsController.stream;

  /// Stream of error events
  Stream<String> get errorStream => _errorController.stream;

  /// Whether the service is currently online
  bool get isOnline => _isOnline;

  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _loggingService.error(
          'PronunciationService', 'Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    _loggingService.info('PronunciationService',
        'Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
  }

  /// Whether to use pronunciation guidance
  bool get usePronunciationGuidance => _usePronunciationGuidance;

  /// Whether to use IPA notation
  bool get useIpaNotation => _useIpaNotation;

  /// Whether to use simplified phonetics
  bool get useSimplifiedPhonetics => _useSimplifiedPhonetics;

  /// Whether to use syllable breakdown
  bool get useSyllableBreakdown => _useSyllableBreakdown;

  /// Whether to show difficult pronunciations only
  bool get showDifficultOnly => _showDifficultOnly;

  /// Whether to auto-play pronunciation audio
  bool get autoPlayPronunciation => _autoPlayPronunciation;

  /// Load preferences
  void _loadPreferences() {
    try {
      _usePronunciationGuidance =
          _prefs.getBool('use_pronunciation_guidance') ?? true;
      _useIpaNotation = _prefs.getBool('use_ipa_notation') ?? true;
      _useSimplifiedPhonetics =
          _prefs.getBool('use_simplified_phonetics') ?? true;
      _useSyllableBreakdown = _prefs.getBool('use_syllable_breakdown') ?? true;
      _showDifficultOnly = _prefs.getBool('show_difficult_only') ?? false;
      _autoPlayPronunciation =
          _prefs.getBool('auto_play_pronunciation') ?? false;
      _loggingService.info(
          'PronunciationService', 'Loaded pronunciation preferences');
    } catch (e) {
      debugPrint('Error loading pronunciation preferences: $e');
      _loggingService.error('PronunciationService',
          'Error loading pronunciation preferences: $e');
      _errorController.add('Error loading pronunciation preferences: $e');
    }
  }

  /// Save preferences
  Future<bool> _savePreferences() async {
    try {
      await _prefs.setBool(
          'use_pronunciation_guidance', _usePronunciationGuidance);
      await _prefs.setBool('use_ipa_notation', _useIpaNotation);
      await _prefs.setBool('use_simplified_phonetics', _useSimplifiedPhonetics);
      await _prefs.setBool('use_syllable_breakdown', _useSyllableBreakdown);
      await _prefs.setBool('show_difficult_only', _showDifficultOnly);
      await _prefs.setBool('auto_play_pronunciation', _autoPlayPronunciation);
      _loggingService.info(
          'PronunciationService', 'Saved pronunciation preferences');
      return true;
    } catch (e) {
      debugPrint('Error saving pronunciation preferences: $e');
      _loggingService.error(
          'PronunciationService', 'Error saving pronunciation preferences: $e');
      _errorController.add('Error saving pronunciation preferences: $e');
      return false;
    }
  }

  /// Set whether to use pronunciation guidance
  Future<void> setUsePronunciationGuidance(bool value) async {
    _usePronunciationGuidance = value;
    await _savePreferences();
  }

  /// Set whether to use IPA notation
  Future<void> setUseIpaNotation(bool value) async {
    _useIpaNotation = value;
    await _savePreferences();
  }

  /// Set whether to use simplified phonetics
  Future<void> setUseSimplifiedPhonetics(bool value) async {
    _useSimplifiedPhonetics = value;
    await _savePreferences();
  }

  /// Set whether to use syllable breakdown
  Future<void> setUseSyllableBreakdown(bool value) async {
    _useSyllableBreakdown = value;
    await _savePreferences();
  }

  /// Set whether to show difficult pronunciations only
  Future<void> setShowDifficultOnly(bool value) async {
    _showDifficultOnly = value;
    await _savePreferences();
  }

  /// Set whether to auto-play pronunciation audio
  Future<void> setAutoPlayPronunciation(bool value) async {
    _autoPlayPronunciation = value;
    await _savePreferences();
  }

  /// Get pronunciation information for a translation
  Future<TranslationPronunciation> getPronunciation({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  }) async {
    if (!_usePronunciationGuidance) {
      _loggingService.info(
          'PronunciationService', 'Pronunciation guidance is disabled');
      return TranslationPronunciation.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }

    // Create a cache key
    final cacheKey = '${sourceLanguage}_${targetLanguage}_${text.hashCode}';

    // Check the cache
    if (_pronunciationCache.containsKey(cacheKey)) {
      _loggingService.info(
          'PronunciationService', 'Using cached pronunciation information');
      return _pronunciationCache[cacheKey]!;
    }

    // Check if we're online for non-cached requests
    if (!_isOnline && !_offlineModeEnabled) {
      const errorMsg = 'Cannot get pronunciation information while offline';
      _loggingService.error('PronunciationService', errorMsg);
      _errorController.add(errorMsg);

      // Return empty pronunciation information
      return TranslationPronunciation.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }

    try {
      // In a real app, we would call an API to get pronunciation information
      // For demo purposes, we'll generate mock data
      final pronunciation = await _generateMockPronunciation(
        text,
        sourceLanguage,
        targetLanguage,
        sourceRegion,
        targetRegion,
      );

      // Cache the result
      _pronunciationCache[cacheKey] = pronunciation;

      // Notify listeners
      _pronunciationEventsController.add(cacheKey);

      _loggingService.info('PronunciationService',
          'Generated pronunciation information for "$text"');

      return pronunciation;
    } catch (e) {
      final errorMsg = 'Error getting pronunciation information: $e';
      debugPrint(errorMsg);
      _loggingService.error('PronunciationService', errorMsg);
      _errorController.add(errorMsg);

      return TranslationPronunciation.empty(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
    }
  }

  /// Generate mock pronunciation information
  Future<TranslationPronunciation> _generateMockPronunciation(
    String text,
    String sourceLanguage,
    String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
  ) async {
    // For demo purposes, we'll generate mock data based on the language pair and text content
    // In a real app, this would come from an API or a database

    final guides = <PronunciationGuide>[];
    String? generalPronunciationNotes;
    String? fullTextAudioPath;

    // Generate language pair specific notes
    if (targetLanguage == 'fr') {
      generalPronunciationNotes =
          'French pronunciation features nasal vowels, silent letters, and liaison between words. '
          'The "r" sound is pronounced at the back of the throat. Stress is generally placed on the last syllable of a word or phrase.';

      // Create a mock audio file for the full text
      final directory = await getTemporaryDirectory();
      fullTextAudioPath =
          '${directory.path}/pronunciation_full_${const Uuid().v4()}.m4a';
      final file = File(fullTextAudioPath);
      await file.writeAsString('Mock audio file');

      // Add some mock pronunciation guides for common French words
      if (text.toLowerCase().contains('bonjour')) {
        final audioPath =
            '${directory.path}/pronunciation_bonjour_${const Uuid().v4()}.m4a';
        await File(audioPath).writeAsString('Mock audio file');

        guides.add(PronunciationGuide(
          id: const Uuid().v4(),
          text: 'bonjour',
          startIndex: text.toLowerCase().indexOf('bonjour'),
          endIndex: text.toLowerCase().indexOf('bonjour') + 'bonjour'.length,
          type: PronunciationGuideType.ipa,
          pronunciation: '/bɔ̃.ʒuʁ/',
          difficulty: PronunciationDifficulty.moderate,
          audioPath: audioPath,
          languageCode: 'fr',
          tips: [
            'The "on" in "bon" is a nasal vowel, pronounced without closing the mouth.',
            'The "j" is pronounced like the "s" in "measure".',
            'The "r" is pronounced at the back of the throat.',
          ],
          commonMistakes: [
            'Pronouncing "on" as in English "on" instead of nasalizing it.',
            'Pronouncing "j" as in English "jam" instead of like "zh".',
          ],
        ));

        guides.add(PronunciationGuide(
          id: const Uuid().v4(),
          text: 'bonjour',
          startIndex: text.toLowerCase().indexOf('bonjour'),
          endIndex: text.toLowerCase().indexOf('bonjour') + 'bonjour'.length,
          type: PronunciationGuideType.simplified,
          pronunciation: 'bohn-ZHOOR',
          difficulty: PronunciationDifficulty.moderate,
          audioPath: audioPath,
          languageCode: 'fr',
        ));

        guides.add(PronunciationGuide(
          id: const Uuid().v4(),
          text: 'bonjour',
          startIndex: text.toLowerCase().indexOf('bonjour'),
          endIndex: text.toLowerCase().indexOf('bonjour') + 'bonjour'.length,
          type: PronunciationGuideType.syllables,
          pronunciation: 'bon·jour',
          difficulty: PronunciationDifficulty.moderate,
          audioPath: audioPath,
          languageCode: 'fr',
        ));
      }

      if (text.toLowerCase().contains('merci')) {
        final audioPath =
            '${directory.path}/pronunciation_merci_${const Uuid().v4()}.m4a';
        await File(audioPath).writeAsString('Mock audio file');

        guides.add(PronunciationGuide(
          id: const Uuid().v4(),
          text: 'merci',
          startIndex: text.toLowerCase().indexOf('merci'),
          endIndex: text.toLowerCase().indexOf('merci') + 'merci'.length,
          type: PronunciationGuideType.ipa,
          pronunciation: '/mɛʁ.si/',
          difficulty: PronunciationDifficulty.easy,
          audioPath: audioPath,
          languageCode: 'fr',
          tips: [
            'The "r" is pronounced at the back of the throat.',
            'The final "i" is pronounced like "ee" in "see".',
          ],
        ));

        guides.add(PronunciationGuide(
          id: const Uuid().v4(),
          text: 'merci',
          startIndex: text.toLowerCase().indexOf('merci'),
          endIndex: text.toLowerCase().indexOf('merci') + 'merci'.length,
          type: PronunciationGuideType.simplified,
          pronunciation: 'mair-SEE',
          difficulty: PronunciationDifficulty.easy,
          audioPath: audioPath,
          languageCode: 'fr',
        ));

        guides.add(PronunciationGuide(
          id: const Uuid().v4(),
          text: 'merci',
          startIndex: text.toLowerCase().indexOf('merci'),
          endIndex: text.toLowerCase().indexOf('merci') + 'merci'.length,
          type: PronunciationGuideType.syllables,
          pronunciation: 'mer·ci',
          difficulty: PronunciationDifficulty.easy,
          audioPath: audioPath,
          languageCode: 'fr',
        ));
      }
    } else if (targetLanguage == 'es') {
      generalPronunciationNotes =
          'Spanish pronunciation is relatively consistent, with each letter generally having one sound. '
          'The "r" is rolled, and the "j" is pronounced like the "h" in "hot" but stronger. Stress is typically on the second-to-last syllable.';

      // Create a mock audio file for the full text
      final directory = await getTemporaryDirectory();
      fullTextAudioPath =
          '${directory.path}/pronunciation_full_${const Uuid().v4()}.m4a';
      final file = File(fullTextAudioPath);
      await file.writeAsString('Mock audio file');

      // Add some mock pronunciation guides for common Spanish words
      if (text.toLowerCase().contains('gracias')) {
        final audioPath =
            '${directory.path}/pronunciation_gracias_${const Uuid().v4()}.m4a';
        await File(audioPath).writeAsString('Mock audio file');

        guides.add(PronunciationGuide(
          id: const Uuid().v4(),
          text: 'gracias',
          startIndex: text.toLowerCase().indexOf('gracias'),
          endIndex: text.toLowerCase().indexOf('gracias') + 'gracias'.length,
          type: PronunciationGuideType.ipa,
          pronunciation: '/ˈɡɾa.θjas/',
          difficulty: PronunciationDifficulty.moderate,
          audioPath: audioPath,
          languageCode: 'es',
          region: 'Spain',
          tips: [
            'The "r" is slightly rolled.',
            'In Spain, the "c" is pronounced like "th" in "thin".',
            'In Latin America, the "c" is pronounced like "s" in "see".',
          ],
          commonMistakes: [
            'Not rolling the "r".',
            'Pronouncing "ci" as "see" in Spain or "thi" in Latin America.',
          ],
        ));

        guides.add(PronunciationGuide(
          id: const Uuid().v4(),
          text: 'gracias',
          startIndex: text.toLowerCase().indexOf('gracias'),
          endIndex: text.toLowerCase().indexOf('gracias') + 'gracias'.length,
          type: PronunciationGuideType.simplified,
          pronunciation: 'GRAH-thyahs (Spain) / GRAH-syahs (Latin America)',
          difficulty: PronunciationDifficulty.moderate,
          audioPath: audioPath,
          languageCode: 'es',
        ));

        guides.add(PronunciationGuide(
          id: const Uuid().v4(),
          text: 'gracias',
          startIndex: text.toLowerCase().indexOf('gracias'),
          endIndex: text.toLowerCase().indexOf('gracias') + 'gracias'.length,
          type: PronunciationGuideType.syllables,
          pronunciation: 'gra·cias',
          difficulty: PronunciationDifficulty.moderate,
          audioPath: audioPath,
          languageCode: 'es',
        ));
      }
    }

    return TranslationPronunciation(
      guides: guides,
      hasPronunciationGuides: guides.isNotEmpty,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
      sourceRegion: sourceRegion,
      targetRegion: targetRegion,
      generalPronunciationNotes: generalPronunciationNotes,
      fullTextAudioPath: fullTextAudioPath,
    );
  }

  /// Whether offline mode is enabled
  bool _offlineModeEnabled = false;

  /// Enable or disable offline mode
  Future<void> setOfflineMode(bool enabled) async {
    _offlineModeEnabled = enabled;
    _loggingService.info('PronunciationService',
        'Offline mode ${enabled ? 'enabled' : 'disabled'}');
    await _prefs.setBool('pronunciation_offline_mode', enabled);
  }

  /// Play pronunciation audio
  Future<void> playPronunciationAudio(String audioPath) async {
    try {
      // Load and play the audio
      await _playbackService.loadAudio(audioPath);
      await _playbackService.play();
      _loggingService.info(
          'PronunciationService', 'Playing pronunciation audio: $audioPath');
    } catch (e) {
      final errorMsg = 'Error playing pronunciation audio: $e';
      debugPrint(errorMsg);
      _loggingService.error('PronunciationService', errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// Stop audio playback
  Future<void> stopAudio() async {
    await _playbackService.stop();
  }

  /// Clear the pronunciation cache
  void clearCache() {
    _pronunciationCache.clear();
    _loggingService.info('PronunciationService', 'Cleared pronunciation cache');
  }

  /// Get the size of the pronunciation cache
  int getCacheSize() {
    return _pronunciationCache.length;
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _pronunciationEventsController.close();
    _errorController.close();
    _client.close();
  }
}

/// Provider for the pronunciation service
final pronunciationServiceProvider = Provider<PronunciationService>((ref) {
  final prefs = refatch(sharedPreferencesProvider);
  final playbackService = refatch(audioPlaybackServiceProvider);
  final client = http.Client();
  final loggingService = LoggingService();
  final connectivity = Connectivity();

  final service = PronunciationService(
    client,
    prefs,
    playbackService,
    loggingService,
    connectivity,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
